"""
Dolphin API for document image parsing.

This module provides API endpoints for parsing document images using the Dolphin model.
The Dolphin model specializes in understanding document layout and extracting structured content.

Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
SPDX-License-Identifier: MIT
"""

import logging
import os

from fastapi import APIRouter, Depends, File, HTTPException, Request, UploadFile

from engine.dolphin import get_dolphin_engine
from server.api.common import ParseResponse
from utils.config import global_config
from vendor.pandora.context.pandora_context import PandoraContext

from .common import *

logger = logging.getLogger(__name__)

dolphin_router = APIRouter(prefix="/dolphin/v1")

# Initialize dolphin engine with proper error handling
try:
    dolphin_engine = get_dolphin_engine(
        global_config.get("dolphin", "model_path"), 
        global_config.get("dolphin", "device")
    )
    logger.info("Dolphin engine initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Dolphin engine: {str(e)}")
    dolphin_engine = None


@dolphin_router.post("/parse_file", response_model=ParseResponse)
async def parse_image(
    file: UploadFile = File(...),
    request: Request = None,
) -> ParseResponse:
    # Check if dolphin engine is available
    if dolphin_engine is None:
        raise HTTPException(
            status_code=503, 
            detail="Dolphin模型未能成功加载，服务暂时不可用"
        )
    
    pc: PandoraContext = request.state.pandora_context
    s1 = pc.root_span.add_span("dolphin_file_upload")
    try:
        tmp_path = await save_upload_file_to_tmp(file)
    except (IOError, OSError) as e:
        s1.set_attribute("error", f"文件保存失败: {str(e)}")
        raise HTTPException(status_code=510, detail=f"文件保存失败: {str(e)}")
    except ValueError as e:
        s1.set_attribute("error", f"文件格式错误: {str(e)}")
        raise HTTPException(status_code=400, detail=f"文件格式错误: {str(e)}")
    except Exception as e:
        s1.set_attribute("error", f"图片保存失败: {str(e)}")
        raise HTTPException(status_code=510, detail=f"图片保存失败: {str(e)}")
    finally:
        s1.finish()

    output_base, md_file_url, _, _, file_name = gen_output_dir_and_urls(request)
    os.makedirs(output_base, exist_ok=True)

    s2 = pc.root_span.add_span("dolphin_file_process")
    try:
        md_content = dolphin_engine.process_page(
            image_path=tmp_path,
            save_dir=output_base,
            base_file_name=file_name,
            max_batch_size=16,
        )
    except Exception as e:
        s2.set_attribute("error", f"图片解析失败: {str(e)}")
        raise HTTPException(status_code=511, detail=f"图片解析失败: {str(e)}")
    finally:
        s2.finish()
        os.unlink(tmp_path)

    return ParseResponse(
        header=ParseHeader(),
        payload=ParseRespPayload(file=md_file_url, markdown=md_content),
    )


@dolphin_router.post("/parse_url", response_model=ParseResponse)
async def parse_url(
    request: Request,
    body: ParseRequest,
) -> ParseResponse:
    pc: PandoraContext = request.state.pandora_context
    s1 = pc.root_span.add_span("dolphin_url_download")
    try:
        file_url = body.payload.file_url
        if not file_url:
            raise HTTPException(
                status_code=400, detail="request payload must contain 'file_url'"
            )
        tmp_path = await download_file_to_tmp(file_url)
    except Exception as e:
        s1.set_attribute("error", f"图片下载失败: {str(e)}")
        raise HTTPException(status_code=510, detail=f"图片下载失败: {str(e)}")
    finally:
        s1.finish()

    output_base, md_file_url, _, _, file_name = gen_output_dir_and_urls(request)
    os.makedirs(output_base, exist_ok=True)

    s2 = pc.root_span.add_span("dolphin_url_process")
    try:
        md_content = dolphin_engine.process_page(
            image_path=tmp_path,
            save_dir=output_base,
            base_file_name=file_name,
            max_batch_size=16,
        )
    except Exception as e:
        s2.set_attribute("error", f"图片解析失败: {str(e)}")
        raise HTTPException(status_code=511, detail=f"图片解析失败: {str(e)}")
    finally:
        s2.finish()
        os.unlink(tmp_path)

    return ParseResponse(
        header=ParseHeader(traceId=body.header.traceId),
        payload=ParseRespPayload(file=md_file_url, markdown=md_content),
    )
