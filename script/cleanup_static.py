#!/usr/bin/env python3
"""
Manual static file cleanup script for the Document Converter Service.

This script allows manual cleanup of old static files without starting the full service.
"""

import argparse
import logging
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from utils.static_cleanup import cleanup_static_files


def setup_logging(verbose: bool = False):
    """Setup logging for the cleanup script."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Clean up old static files")
    parser.add_argument(
        "--static-dir", 
        default="static", 
        help="Path to static directory (default: static)"
    )
    parser.add_argument(
        "--max-age-days", 
        type=int, 
        default=7, 
        help="Maximum age of files to keep in days (default: 7)"
    )
    parser.add_argument(
        "--max-files", 
        type=int, 
        default=1000, 
        help="Maximum number of result directories to keep (default: 1000)"
    )
    parser.add_argument(
        "--dry-run", 
        action="store_true", 
        help="Show what would be deleted without actually deleting"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true", 
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    setup_logging(args.verbose)
    logger = logging.getLogger(__name__)
    
    logger.info("🧹 Static File Cleanup Tool")
    logger.info("=" * 50)
    
    if args.dry_run:
        logger.info("DRY RUN MODE - No files will be deleted")
        # TODO: Implement dry run functionality in StaticFileManager
        logger.warning("Dry run mode not yet implemented. Would cleanup with:")
        logger.info(f"  Static directory: {args.static_dir}")
        logger.info(f"  Max age: {args.max_age_days} days")
        logger.info(f"  Max files: {args.max_files}")
        return
    
    try:
        # Perform cleanup
        result = cleanup_static_files(
            static_dir=args.static_dir,
            max_age_days=args.max_age_days,
            max_files=args.max_files
        )
        
        # Display results
        logger.info("Cleanup Results:")
        logger.info("-" * 30)
        logger.info(f"📁 Deleted directories: {result['deleted_directories']}")
        logger.info(f"💾 Freed space: {result['freed_mb']:.2f} MB")
        
        before = result['before_stats']
        after = result['after_stats']
        
        logger.info(f"\nBefore cleanup: {before['total_directories']} dirs, {before['total_size_mb']:.2f} MB")
        logger.info(f"After cleanup:  {after['total_directories']} dirs, {after['total_size_mb']:.2f} MB")
        
        if result['deleted_directories'] > 0:
            logger.info(f"\n✅ Successfully cleaned up {result['deleted_directories']} old directories")
        else:
            logger.info(f"\n✅ No cleanup needed - all files are within limits")
            
    except Exception as e:
        logger.error(f"❌ Cleanup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()