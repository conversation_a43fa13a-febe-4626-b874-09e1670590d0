#!/usr/bin/env python3
"""
项目健康检查脚本

检查项目的各个方面，包括代码质量、配置、依赖等。
"""

import os
import sys
import ast
import subprocess
from pathlib import Path
from typing import List, Dict, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class ProjectHealthChecker:
    """项目健康检查器"""
    
    def __init__(self):
        self.project_root = project_root
        self.issues = []
        self.warnings = []
        self.successes = []
    
    def log_success(self, message: str):
        """记录成功信息"""
        self.successes.append(f"✅ {message}")
        print(f"✅ {message}")
    
    def log_warning(self, message: str):
        """记录警告信息"""
        self.warnings.append(f"⚠️  {message}")
        print(f"⚠️  {message}")
    
    def log_issue(self, message: str):
        """记录问题信息"""
        self.issues.append(f"❌ {message}")
        print(f"❌ {message}")
    
    def check_file_structure(self):
        """检查文件结构"""
        print("\n📁 检查项目文件结构...")
        
        required_files = [
            "main.py",
            "requirements.txt",
            "README.md",
            "LICENSE",
            "CONTRIBUTING.md",
            "config/config.ini",
            "config/magic-pdf.json"
        ]
        
        required_dirs = [
            "server/api",
            "utils/config",
            "utils/logger",
            "utils/graceful_stop",
            "test",
            "docs",
            "script"
        ]
        
        for file_path in required_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self.log_success(f"文件存在: {file_path}")
            else:
                self.log_issue(f"缺少文件: {file_path}")
        
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if full_path.exists() and full_path.is_dir():
                self.log_success(f"目录存在: {dir_path}")
            else:
                self.log_issue(f"缺少目录: {dir_path}")
    
    def check_python_syntax(self):
        """检查Python语法"""
        print("\n🐍 检查Python语法...")
        
        python_files = list(self.project_root.rglob("*.py"))
        syntax_errors = 0
        
        for py_file in python_files:
            if "/__pycache__/" in str(py_file) or "/.git/" in str(py_file):
                continue
            
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                ast.parse(content)
                
            except SyntaxError as e:
                self.log_issue(f"语法错误 {py_file}: {e}")
                syntax_errors += 1
            except Exception as e:
                self.log_warning(f"无法解析 {py_file}: {e}")
        
        if syntax_errors == 0:
            self.log_success(f"所有Python文件语法正确 ({len(python_files)} 个文件)")
        else:
            self.log_issue(f"发现 {syntax_errors} 个语法错误")
    
    def check_imports(self):
        """检查关键模块导入"""
        print("\n📦 检查关键模块导入...")
        
        key_modules = [
            ("utils.config", "配置管理"),
            ("server.api.common", "API通用模块"),
            ("server.api.mineru_api", "Mineru API"),
            ("server.api.dolphin_api", "Dolphin API"),
            ("server.api.markitdown_api", "MarkItDown API"),
            ("utils.graceful_stop.stop", "优雅关闭")
        ]
        
        for module_name, description in key_modules:
            try:
                __import__(module_name)
                self.log_success(f"{description} 模块导入成功")
            except ImportError as e:
                self.log_issue(f"{description} 模块导入失败: {e}")
            except Exception as e:
                self.log_warning(f"{description} 模块导入异常: {e}")
    
    def check_configuration(self):
        """检查配置文件"""
        print("\n⚙️  检查配置文件...")
        
        # 检查环境变量
        env_vars = [
            "CONFIG_FILE_PATH",
            "MINERU_TOOLS_CONFIG_JSON"
        ]
        
        for var in env_vars:
            value = os.getenv(var)
            if value:
                if os.path.exists(value):
                    self.log_success(f"环境变量 {var} 配置正确: {value}")
                else:
                    self.log_issue(f"环境变量 {var} 指向的文件不存在: {value}")
            else:
                self.log_warning(f"环境变量 {var} 未设置")
        
        # 检查配置文件内容
        try:
            import utils.config
            config = utils.config.global_config
            
            required_sections = ["version", "server", "elk", "dolphin"]
            for section in required_sections:
                if config.has_section(section):
                    self.log_success(f"配置段 [{section}] 存在")
                else:
                    self.log_warning(f"配置段 [{section}] 缺失")
        
        except Exception as e:
            self.log_issue(f"配置文件检查失败: {e}")
    
    def check_dependencies(self):
        """检查依赖"""
        print("\n📚 检查关键依赖...")
        
        critical_deps = [
            "fastapi",
            "uvicorn", 
            "pydantic",
            "httpx"
        ]
        
        optional_deps = [
            "magic_pdf",
            "markitdown",
            "transformers",
            "torch"
        ]
        
        for dep in critical_deps:
            try:
                __import__(dep)
                self.log_success(f"关键依赖 {dep} 已安装")
            except ImportError:
                self.log_issue(f"关键依赖 {dep} 未安装")
        
        for dep in optional_deps:
            try:
                __import__(dep)
                self.log_success(f"可选依赖 {dep} 已安装")
            except ImportError:
                self.log_warning(f"可选依赖 {dep} 未安装")
    
    def check_demo_files(self):
        """检查演示文件"""
        print("\n🎯 检查演示文件...")
        
        demo_dir = self.project_root / "demo"
        if not demo_dir.exists():
            self.log_warning("demo 目录不存在")
            return
        
        subdirs = ["pdf", "img", "office"]
        for subdir in subdirs:
            subdir_path = demo_dir / subdir
            if subdir_path.exists():
                files = list(subdir_path.glob("*"))
                if files:
                    self.log_success(f"demo/{subdir} 包含 {len(files)} 个文件")
                else:
                    self.log_warning(f"demo/{subdir} 目录为空")
            else:
                self.log_warning(f"demo/{subdir} 目录不存在")
    
    def check_documentation(self):
        """检查文档完整性"""
        print("\n📖 检查文档完整性...")
        
        doc_files = [
            ("README.md", "项目说明"),
            ("LICENSE", "许可证"),
            ("CONTRIBUTING.md", "贡献指南"),
            ("docs/API.md", "API文档"),
            ("docs/OPTIMIZATION_SUMMARY.md", "优化总结")
        ]
        
        for file_path, description in doc_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                # 检查文件大小
                size = full_path.stat().st_size
                if size > 100:  # 至少100字节
                    self.log_success(f"{description} 文档存在且有内容")
                else:
                    self.log_warning(f"{description} 文档存在但内容较少")
            else:
                self.log_issue(f"{description} 文档缺失: {file_path}")
    
    def generate_report(self):
        """生成检查报告"""
        print("\n" + "="*60)
        print("📊 项目健康检查报告")
        print("="*60)
        
        print(f"\n✅ 成功项目: {len(self.successes)}")
        print(f"⚠️  警告项目: {len(self.warnings)}")
        print(f"❌ 问题项目: {len(self.issues)}")
        
        if self.issues:
            print(f"\n🚨 需要修复的问题 ({len(self.issues)}):")
            for issue in self.issues:
                print(f"  {issue}")
        
        if self.warnings:
            print(f"\n⚠️  需要关注的警告 ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  {warning}")
        
        # 计算健康分数
        total_checks = len(self.successes) + len(self.warnings) + len(self.issues)
        if total_checks > 0:
            health_score = (len(self.successes) + len(self.warnings) * 0.5) / total_checks * 100
            print(f"\n🎯 项目健康分数: {health_score:.1f}%")
            
            if health_score >= 90:
                print("🎉 项目状态优秀！")
            elif health_score >= 75:
                print("👍 项目状态良好")
            elif health_score >= 60:
                print("⚠️  项目状态一般，建议改进")
            else:
                print("🚨 项目状态需要重点关注")
        
        return len(self.issues) == 0
    
    def run_all_checks(self):
        """运行所有检查"""
        print("🔍 开始项目健康检查...")
        
        self.check_file_structure()
        self.check_python_syntax()
        self.check_imports()
        self.check_configuration()
        self.check_dependencies()
        self.check_demo_files()
        self.check_documentation()
        
        return self.generate_report()

def main():
    """主函数"""
    checker = ProjectHealthChecker()
    success = checker.run_all_checks()
    
    if success:
        print("\n🎉 项目健康检查通过！")
        sys.exit(0)
    else:
        print("\n⚠️  项目健康检查发现问题，请查看上述报告")
        sys.exit(1)

if __name__ == "__main__":
    main()
