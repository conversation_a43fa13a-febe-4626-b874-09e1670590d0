#!/usr/bin/env python3
"""
Test script to verify the service can start and respond to basic requests.
"""

import os
import sys
import time
import requests
import subprocess
import signal
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_service_startup():
    """Test that the service can start up successfully."""
    print("🚀 Testing service startup...")
    
    # Change to project directory
    os.chdir(project_root)
    
    # Start the service in background
    try:
        # Use uvicorn directly to start the service
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8001",  # Use different port to avoid conflicts
            "--log-level", "info"
        ]
        
        print(f"Starting service with command: {' '.join(cmd)}")
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait a bit for the service to start
        print("Waiting for service to start...")
        time.sleep(10)
        
        # Check if process is still running
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ Service failed to start")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return False
        
        # Test basic endpoints
        base_url = "http://localhost:8001"
        
        # Test health endpoint
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Health endpoint working")
                print(f"Response: {response.json()}")
            else:
                print(f"❌ Health endpoint failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Health endpoint error: {e}")
        
        # Test root endpoint
        try:
            response = requests.get(f"{base_url}/", timeout=5)
            if response.status_code == 200:
                print("✅ Root endpoint working")
                print(f"Response: {response.json()}")
            else:
                print(f"❌ Root endpoint failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Root endpoint error: {e}")
        
        # Test docs endpoint
        try:
            response = requests.get(f"{base_url}/docs", timeout=5)
            if response.status_code == 200:
                print("✅ Docs endpoint working")
            else:
                print(f"❌ Docs endpoint failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Docs endpoint error: {e}")
        
        print("✅ Service startup test completed")
        
        # Cleanup: terminate the process
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()
        
        return True
        
    except Exception as e:
        print(f"❌ Service startup test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_demo_file_processing():
    """Test processing demo files."""
    print("\n📁 Testing demo file processing...")
    
    demo_dir = project_root / "demo"
    
    # Check if demo files exist
    pdf_files = list((demo_dir / "pdf").glob("*.pdf"))
    img_files = list((demo_dir / "img").glob("*"))
    office_files = list((demo_dir / "office").glob("*"))
    
    print(f"Found {len(pdf_files)} PDF files")
    print(f"Found {len(img_files)} image files") 
    print(f"Found {len(office_files)} office files")
    
    if pdf_files:
        print(f"Sample PDF: {pdf_files[0].name}")
    if img_files:
        print(f"Sample image: {img_files[0].name}")
    if office_files:
        print(f"Sample office file: {office_files[0].name}")
    
    print("✅ Demo files are available for testing")
    return True

def main():
    """Run all tests."""
    print("🧪 Document Converter Service Test Suite")
    print("=" * 50)
    
    tests = [
        test_demo_file_processing,
        test_service_startup,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print("-" * 30)
    
    print(f"\n📊 Test Summary:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed!")
        return True
    else:
        print(f"\n⚠️  {failed} test(s) failed.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
