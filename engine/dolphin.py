import cv2
import logging
import torch
from PIL import Image
from transformers import AutoProcessor, VisionEncoderDecoderModel

from utils.dolphin.utils import *

logger = logging.getLogger(__name__)


class DOLPHIN:
    def __init__(self, model_id_or_path, device="cuda"):
        self.processor = AutoProcessor.from_pretrained(model_id_or_path, use_fast=True)
        self.model = VisionEncoderDecoderModel.from_pretrained(model_id_or_path)
        self.model.eval()
        self.device = device if torch.cuda.is_available() else "cpu"
        self.model.to(self.device)
        self.model = self.model.half()
        self.tokenizer = self.processor.tokenizer

    def chat(self, prompt, image):
        is_batch = isinstance(image, list)
        if not is_batch:
            images = [image]
            prompts = [prompt]
        else:
            images = image
            prompts = prompt if isinstance(prompt, list) else [prompt] * len(images)
        batch_inputs = self.processor(images, return_tensors="pt", padding=True)
        batch_pixel_values = batch_inputs.pixel_values.half().to(self.device)
        prompts = [f"<s>{p} <Answer/>" for p in prompts]
        batch_prompt_inputs = self.tokenizer(
            prompts, add_special_tokens=False, return_tensors="pt"
        )
        batch_prompt_ids = batch_prompt_inputs.input_ids.to(self.device)
        batch_attention_mask = batch_prompt_inputs.attention_mask.to(self.device)
        outputs = self.model.generate(
            pixel_values=batch_pixel_values,
            decoder_input_ids=batch_prompt_ids,
            decoder_attention_mask=batch_attention_mask,
            min_length=1,
            max_length=4096,
            pad_token_id=self.tokenizer.pad_token_id,
            eos_token_id=self.tokenizer.eos_token_id,
            use_cache=True,
            bad_words_ids=[[self.tokenizer.unk_token_id]],
            return_dict_in_generate=True,
            do_sample=False,
            num_beams=1,
            repetition_penalty=1.1,
        )
        sequences = self.tokenizer.batch_decode(
            outputs.sequences, skip_special_tokens=False
        )
        results = []
        for i, sequence in enumerate(sequences):
            cleaned = (
                sequence.replace(prompts[i], "")
                .replace("<pad>", "")
                .replace("</s>", "")
                .strip()
            )
            results.append(cleaned)
        if not is_batch:
            return results[0]
        return results

    def process_page(self, image_path, save_dir, base_file_name, max_batch_size=None):
        """Parse document images with two stages"""
        # Stage 1: Page-level layout and reading order parsing
        pil_image = Image.open(image_path).convert("RGB")
        layout_output = self.chat(
            "Parse the reading order of this document.", pil_image
        )

        # Stage 2: Element-level content parsing
        padded_image, dims = prepare_image(pil_image)
        recognition_results = self.process_elements(
            layout_output, padded_image, dims, max_batch_size
        )

        # Save outputs
        markdown_content = save_outputs(recognition_results, save_dir, base_file_name)

        return markdown_content

    def process_elements(self, layout_results, padded_image, dims, max_batch_size=None):
        """Parse all document elements with parallel decoding"""
        layout_results = parse_layout_string(layout_results)

        # Store text and table elements separately
        text_elements = []  # Text elements
        table_elements = []  # Table elements
        figure_results = []  # Image elements (no processing needed)
        previous_box = None
        reading_order = 0

        # Collect elements to process and group by type
        for bbox, label in layout_results:
            try:
                # Adjust coordinates
                x1, y1, x2, y2, orig_x1, orig_y1, orig_x2, orig_y2, previous_box = (
                    process_coordinates(bbox, padded_image, dims, previous_box)
                )

                # Crop and parse element
                cropped = padded_image[y1:y2, x1:x2]
                if cropped.size > 0:
                    if label == "fig":
                        # For figure regions, add empty text result immediately
                        figure_results.append(
                            {
                                "label": label,
                                "bbox": [orig_x1, orig_y1, orig_x2, orig_y2],
                                "text": "",
                                "reading_order": reading_order,
                            }
                        )
                    else:
                        # Prepare element for parsing
                        pil_crop = Image.fromarray(
                            cv2.cvtColor(cropped, cv2.COLOR_BGR2RGB)
                        )
                        element_info = {
                            "crop": pil_crop,
                            "label": label,
                            "bbox": [orig_x1, orig_y1, orig_x2, orig_y2],
                            "reading_order": reading_order,
                        }

                        # Group by type
                        if label == "tab":
                            table_elements.append(element_info)
                        else:  # Text elements
                            text_elements.append(element_info)

                reading_order += 1

            except Exception as e:
                logger.error(f"Error processing bbox with label {label}: {str(e)}")
                continue

        # Initialize results list
        recognition_results = figure_results.copy()

        # Process text elements (in batches)
        if text_elements:
            text_results = self.process_element_batch(
                text_elements, "Read text in the image.", max_batch_size
            )
            recognition_results.extend(text_results)

        # Process table elements (in batches)
        if table_elements:
            table_results = self.process_element_batch(
                table_elements, "Parse the table in the image.", max_batch_size
            )
            recognition_results.extend(table_results)

        # Sort elements by reading order
        recognition_results.sort(key=lambda x: x.get("reading_order", 0))

        return recognition_results

    def process_element_batch(self, elements, prompt, max_batch_size=None):
        """Process elements of the same type in batches"""
        results = []

        # Determine batch size
        batch_size = len(elements)
        if max_batch_size is not None and max_batch_size > 0:
            batch_size = min(batch_size, max_batch_size)

        # Process in batches
        for i in range(0, len(elements), batch_size):
            batch_elements = elements[i : i + batch_size]
            crops_list = [elem["crop"] for elem in batch_elements]

            # Use the same prompt for all elements in the batch
            prompts_list = [prompt] * len(crops_list)

            # Batch inference
            batch_results = self.chat(prompts_list, crops_list)

            # Add results
            for j, result in enumerate(batch_results):
                elem = batch_elements[j]
                results.append(
                    {
                        "label": elem["label"],
                        "bbox": elem["bbox"],
                        "text": result.strip(),
                        "reading_order": elem["reading_order"],
                    }
                )

        return results


def get_dolphin_engine(model_id_or_path, device="cuda"):
    """
    Get a DOLPHIN engine instance.
    :param model_id_or_path: Model ID or path to the DOLPHIN model.
    :param device: Device to run the model on (default is "cuda").
    :return: An instance of DolphinEngine.
    """
    return DOLPHIN(model_id_or_path, device)
